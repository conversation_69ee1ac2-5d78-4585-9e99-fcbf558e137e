# MCDEV-10583 Fix - Test Plan

## Issue Summary
When updating a recurring event's subTitle field, the change was not being propagated to other instances in the series, even though:
- subTitle IS correctly carried forward when initially creating a recurring event
- Other fields like Event Description and internalNotes are properly updated across all recurring event instances

## Root Cause
The `ev_updateUpcomingRecurringEvents` stored procedure only updated events with `startTime > @eventStartDate`, which excluded:
1. Events with the same start time as the current event being edited
2. All events when editing the last event in a series (no future events)

## Fix Applied
Changed the condition from `startTime > @eventStartDate` to `startTime >= @eventStartDate AND e.eventID != @copyFromEventID` to ensure all future events in the series get updated while avoiding updating the source event twice.

## Test Scenarios

### Test Case 1: Update subTitle on First Event in Series
**Setup:**
1. Create a recurring event series with 5 events (Event A, B, C, D, E)
2. All events should have the same initial subTitle: "Original SubTitle"

**Test Steps:**
1. Edit Event A (first event)
2. Change subTitle to "Updated SubTitle - Test 1"
3. Check "Update Upcoming Recurring Events"
4. Save the event

**Expected Results:**
- Event A: subTitle = "Updated SubTitle - Test 1"
- Events B, C, D, E: subTitle = "Updated SubTitle - Test 1"

### Test Case 2: Update subTitle on Middle Event in Series
**Setup:**
1. Use the same recurring event series from Test Case 1
2. Reset all subTitles to "Middle Test SubTitle"

**Test Steps:**
1. Edit Event C (middle event)
2. Change subTitle to "Updated SubTitle - Test 2"
3. Check "Update Upcoming Recurring Events"
4. Save the event

**Expected Results:**
- Events A, B: subTitle = "Middle Test SubTitle" (unchanged)
- Event C: subTitle = "Updated SubTitle - Test 2"
- Events D, E: subTitle = "Updated SubTitle - Test 2"

### Test Case 3: Update subTitle on Last Event in Series (Critical Test)
**Setup:**
1. Use the same recurring event series
2. Reset all subTitles to "Last Test SubTitle"

**Test Steps:**
1. Edit Event E (last event)
2. Change subTitle to "Updated SubTitle - Test 3"
3. Check "Update Upcoming Recurring Events"
4. Save the event

**Expected Results:**
- Events A, B, C, D: subTitle = "Last Test SubTitle" (unchanged)
- Event E: subTitle = "Updated SubTitle - Test 3"
- **This test verifies the fix works when there are no future events**

### Test Case 4: Update subTitle with Same Start Times
**Setup:**
1. Create a recurring event series where multiple events have the same start time
2. Set initial subTitle to "Same Time SubTitle"

**Test Steps:**
1. Edit one of the events with the same start time
2. Change subTitle to "Updated Same Time SubTitle"
3. Check "Update Upcoming Recurring Events"
4. Save the event

**Expected Results:**
- The edited event: subTitle = "Updated Same Time SubTitle"
- Other events with same or later start times: subTitle = "Updated Same Time SubTitle"
- Events with earlier start times: subTitle = "Same Time SubTitle" (unchanged)

### Test Case 5: Verify Other Fields Still Work (Regression Test)
**Setup:**
1. Use any recurring event series
2. Set initial values for subTitle, internalNotes, and Event Description

**Test Steps:**
1. Edit any event in the series
2. Change subTitle, internalNotes, and Event Description
3. Check "Update Upcoming Recurring Events"
4. Save the event

**Expected Results:**
- All three fields (subTitle, internalNotes, Event Description) should be properly propagated to future events
- **This ensures the fix doesn't break existing functionality**

### Test Case 6: Verify No Update When Checkbox Unchecked
**Setup:**
1. Use any recurring event series
2. Set initial subTitle to "No Update Test"

**Test Steps:**
1. Edit any event in the series
2. Change subTitle to "Should Not Propagate"
3. **DO NOT** check "Update Upcoming Recurring Events"
4. Save the event

**Expected Results:**
- Only the edited event should have subTitle = "Should Not Propagate"
- All other events should retain subTitle = "No Update Test"

## Database Verification Queries

After each test, run these queries to verify the results:

```sql
-- Check subTitle values for all events in a series
SELECT e.eventID, e.eventSubTitle, et.startTime, e.recurringSeriesID
FROM dbo.ev_events e
INNER JOIN dbo.ev_times et ON et.eventID = e.eventID AND et.timeID = e.defaultTimeID
WHERE e.recurringSeriesID = [SERIES_ID]
ORDER BY et.startTime;

-- Check if events are properly queued for import (during update process)
SELECT * FROM platformQueue.dbo.tblQueue 
WHERE queueTypeID = (SELECT queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'ImportEvents')
AND dateProcessed IS NULL
ORDER BY dateQueued DESC;
```

## Success Criteria
1. All test cases pass as expected
2. subTitle changes propagate correctly to future events in all scenarios
3. No regression in existing functionality (internalNotes, Event Description still work)
4. No performance degradation in the update process
5. Database integrity maintained (no orphaned records, proper referential integrity)

## Rollback Plan
If issues are discovered:
1. Revert the stored procedure change by running the original version
2. The original condition was: `AND et.startTime > @eventStartDate`
3. Remove the `AND e.eventID != @copyFromEventID` condition

## Notes
- The fix is backward compatible and doesn't change the API or user interface
- The change only affects the server-side logic for propagating updates
- No changes to the frontend or ColdFusion code are required
