ALTER PROC dbo.ev_importEvents_validate
@siteid int, 
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @ASID int, @crdAuthorityCode varchar(20), @creditColName varchar(50), @minColID int, @mincol varchar(255), 
		@good bit, @reqMsg varchar(800), @isRequired bit, @dataTypeCode varchar(12), @displayTypeCode varchar(12), 
		@recurringEvents bit, @dynSQL nvarchar(max), @queueTypeID int;

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='importEvents', @queueTypeID=@queueTypeID OUTPUT;
	
	set @importResult = null;

	SELECT @recurringEvents = recurringEvents
	FROM dbo.siteFeatures 
	WHERE siteID = @siteID;

	-- ***********
	-- clean table 
	-- ***********
	BEGIN TRY
		-- delete empty rows
		delete from #mc_EvImport where Calendar is null and EventTitle is null;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to clean import table.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ****************
	-- required columns 
	-- ****************
	BEGIN TRY
		-- match on calendar
		update tmp 
		set tmp.MCCalendarID = cals.calendarID 
		from dbo.fn_ev_getCalendarsOnSite(@siteid) as cals
		cross apply dbo.fn_cms_getApplicationInstancePagePath(@siteid,cals.applicationInstanceID) as aip
		inner join #mc_EvImport as tmp on isnull(tmp.calendar,'') = aip.pagename
		where aip.applicationSiteResourceID = cals.siteResourceID
		and aip.applicationSiteResourceType = 'Events';

		-- check for missing calendars
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN MCCalendarID int not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ': ' + isnull(calendar,'') + ' does not match an existing calendar.'
			FROM #mc_EvImport
			WHERE MCCalendarID IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- no blank eventTitles
		update #mc_EvImport set eventTitle = '' where eventTitle is null;

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventTitle.'
		FROM #mc_EvImport
		WHERE eventTitle = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- eventTitles must be at or under 200 chars
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid EventTitle. EventTitles must be 200 characters or less.'
		FROM #mc_EvImport
		WHERE len(EventTitle) > 200 
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;
			
		-- EventSubTitle must be at or under 200 chars
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid EventSubTitle. EventSubTitle must be 200 characters or less.'
		FROM #mc_EvImport
		WHERE len(isnull(EventSubTitle,'')) > 200 
		ORDER BY rowID;

		-- no blank eventCodes
		update #mc_EvImport set eventCode = '' where eventCode is null;

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventCode.'
		FROM #mc_EvImport
		WHERE eventCode = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- eventcode must be at or under 15 chars
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ': ' + eventCode + ' has an invalid EventCode. EventCodes must be 15 characters or less.'
		FROM #mc_EvImport
		WHERE len(eventCode) > 15 
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- eventcode must be unique in file
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN eventCode varchar(15) not null;
			ALTER TABLE #mc_EvImport ADD PRIMARY KEY (eventCode);
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'EventCode ' + eventCode + ' appears in the file multiple times. EventCodes must be unique.'
			FROM #mc_EvImport
			GROUP BY eventCode
			HAVING count(*) > 1
			ORDER BY eventCode;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- match on eventCode
		update tmp 
		set tmp.MCEventID = e.eventID
		from #mc_EvImport as tmp 
		inner join dbo.ev_events as e on e.reportCode = tmp.eventCode
			and e.status = 'A'
			and e.siteID = @siteID
		inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
			and ce.calendarID = tmp.MCCalendarID
			and ce.calendarID = ce.sourceCalendarID;

		-- lookup registration
		update tmp 
		set tmp.MCRegistrationID = r.registrationID
		from #mc_EvImport as tmp 
		inner join dbo.ev_registration as r on r.eventID = tmp.MCEventID 
			and r.siteID = @siteID
			and r.status = 'A'
			and r.registrationTypeID = 1
		where tmp.MCEventID is not null;


		/* EVENT CATEGORIES */
		-- no blank EventCategory
		update #mc_EvImport set EventCategory = '' where EventCategory is null;

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventCategory.'
		FROM #mc_EvImport
		WHERE EventCategory = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- split category into separate table
		INSERT INTO #tblEVCategories (MCCalendarID, rowID, EventCategory, CategoryOrder)
		SELECT tmp.MCCalendarID, tmp.rowID, li.listItem, MIN(li.autoID)
		FROM #mc_EvImport as tmp
		CROSS APPLY dbo.fn_varcharListToTable(tmp.EventCategory,'|') as li
		GROUP BY tmp.MCCalendarID, tmp.rowID, li.listItem;

		-- match on EventCategory
		update tmp 
		set tmp.MCCategoryID = cat.categoryID  
		from #tblEVCategories as tmp 
		inner join dbo.ev_calendars as c on c.siteID = @siteID and c.calendarID = tmp.MCCalendarID
		inner join dbo.ev_categories as cat on cat.calendarID = c.calendarID and cat.category = tmp.EventCategory;

		-- check for missing EventCategory
		BEGIN TRY
			ALTER TABLE #tblEVCategories ALTER COLUMN MCCategoryID int not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			SELECT distinct 'EventCategory ' + EventCategory + ' does not match an existing category for that calendar.'
			FROM #tblEVCategories
			WHERE MCCategoryID IS NULL
			ORDER BY 1;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH


		-- put ordered categoryID list back onto events table
		IF OBJECT_ID('tempdb..#tmpRankedCategories') IS NOT NULL 
			DROP TABLE #tmpRankedCategories;
		IF OBJECT_ID('tempdb..#tmpEVCatConcatenations') IS NOT NULL 
			DROP TABLE #tmpEVCatConcatenations;
		IF OBJECT_ID('tempdb..#tmpEVCatRankedConcatenations') IS NOT NULL 
			DROP TABLE #tmpEVCatRankedConcatenations;
		create table #tmpRankedCategories (rowID int, categoryID varchar(max), categoryOrder int);
		CREATE TABLE #tmpEVCatConcatenations (rowID int, CategoryIDs varchar(max), categoryOrder int);
		CREATE TABLE #tmpEVCatRankedConcatenations (rowID int, CategoryIDs varchar(max), categoryOrder int);

		INSERT INTO #tmpRankedCategories
		SELECT rowID, cast(MCCategoryID as varchar(max)) as categoryID, 
			ROW_NUMBER() OVER (PARTITION BY rowID ORDER BY CategoryOrder) as categoryOrder
		from #tblEVCategories;

		WITH Concatenations AS (
			SELECT rowID, categoryID as CategoryIDs, categoryOrder
			FROM #tmpRankedCategories
			WHERE categoryOrder = 1
				union all
			SELECT c.rowID, (c.CategoryIDs + '|' + l.categoryID) as CategoryIDs, l.categoryOrder
			FROM Concatenations as c
			INNER JOIN #tmpRankedCategories as l ON l.rowID = c.rowID AND l.categoryOrder = c.categoryOrder + 1
		)
		INSERT INTO #tmpEVCatConcatenations (rowID, CategoryIDs, categoryOrder)
		SELECT rowID, CategoryIDs, categoryOrder
		FROM Concatenations;	
		
		INSERT INTO #tmpEVCatRankedConcatenations (rowID, CategoryIDs, categoryOrder)
		SELECT rowID, CategoryIDs, ROW_NUMBER() OVER (PARTITION BY rowID ORDER BY categoryOrder DESC) as categoryOrder
		FROM #tmpEVCatConcatenations;

		UPDATE tmp
		set tmp.MCCategoryIDList = c.CategoryIDs
		from #mc_EvImport as tmp
		inner join #tmpEVCatRankedConcatenations as c on c.rowID = tmp.rowID and c.categoryOrder = 1;

		IF OBJECT_ID('tempdb..#tmpRankedCategories') IS NOT NULL 
			DROP TABLE #tmpRankedCategories;
		IF OBJECT_ID('tempdb..#tmpEVCatConcatenations') IS NOT NULL 
			DROP TABLE #tmpEVCatConcatenations;
		IF OBJECT_ID('tempdb..#tmpEVCatRankedConcatenations') IS NOT NULL 
			DROP TABLE #tmpEVCatRankedConcatenations;

		-- check for missing EventCategory again
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN MCCategoryIDList varchar(max) not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing EventCategory.'
			FROM #mc_EvImport
			WHERE MCCategoryIDList IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- ensure EventStart is datetime (allow nulls for this check)
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventStart datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('The column EventStart contains invalid dates.');

			GOTO on_done;
		END CATCH

		-- check for null EventStart
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventStart datetime not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required EventStart.'
			FROM #mc_EvImport
			WHERE EventStart IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- ensure EventEnd is datetime (allow nulls for this check)
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventEnd datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('The column EventEnd contains invalid dates.');

			GOTO on_done;
		END CATCH

		-- check for null EventEnd
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventEnd datetime not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required EventEnd.'
			FROM #mc_EvImport
			WHERE EventEnd IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- check dates
		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a EventStart after the EventEnd.'
		FROM #mc_EvImport
		WHERE EventStart > EventEnd
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- no blank RegistrationReplyEmail (skip check for recurring events)
		update #mc_EvImport set RegistrationReplyEmail = '' where RegistrationReplyEmail is null;

		INSERT INTO #tblEvErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(tmp.rowID as varchar(10)) + ' has a missing RegistrationReplyEmail.'
		FROM #mc_EvImport AS tmp
		LEFT OUTER JOIN dbo.ev_events AS e ON e.siteID = @siteID
			AND e.eventID = tmp.MCEventID
			AND e.recurringSeriesID IS NOT NULL
		WHERE tmp.RegistrationReplyEmail = ''
		AND ISNULL(tmp.RecurringSeriesCode,'') = ''
		AND e.eventID IS NULL
		ORDER BY tmp.rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to validate data in required columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ****************
	-- optional columns 
	-- ****************
	BEGIN TRY
		-- bit columns provided in the original upload need a value for each row
		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'EventHidden') BEGIN
			UPDATE #mc_EvImport set EventHidden = '' where EventHidden is null;
			UPDATE #mc_EvImport set EventHidden = '1' where EventHidden in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set EventHidden = '0' where EventHidden in ('No','N','FALSE');
			
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the EventHidden column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE EventHidden = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN EventHidden bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the EventHidden column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'EventAllDay') BEGIN
			UPDATE #mc_EvImport set EventAllDay = '' where EventAllDay is null;
			UPDATE #mc_EvImport set EventAllDay = '1' where EventAllDay in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set EventAllDay = '0' where EventAllDay in ('No','N','FALSE');
			UPDATE #mc_EvImport set EventAllDay = '1' where EventStart = EventEnd;

			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the EventAllDay column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE EventAllDay = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN EventAllDay bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the EventAllDay column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'EnableRealTimeRoster') BEGIN
			UPDATE #mc_EvImport set EnableRealTimeRoster = '' where EnableRealTimeRoster is null;
			UPDATE #mc_EvImport set EnableRealTimeRoster = '1' where EnableRealTimeRoster in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set EnableRealTimeRoster = '0' where EnableRealTimeRoster in ('No','N','FALSE');
			
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the EnableRealTimeRoster column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE EnableRealTimeRoster = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN EnableRealTimeRoster bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the EnableRealTimeRoster column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'DisplayCredits') BEGIN
			UPDATE #mc_EvImport set DisplayCredits = '' where DisplayCredits is null;
			UPDATE #mc_EvImport set DisplayCredits = '1' where DisplayCredits in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set DisplayCredits = '0' where DisplayCredits in ('No','N','FALSE');

			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the DisplayCredits column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE DisplayCredits = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN DisplayCredits bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the DisplayCredits column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'ContactInclude') BEGIN
			UPDATE #mc_EvImport set ContactInclude = '' where ContactInclude is null;
			UPDATE #mc_EvImport set ContactInclude = '1' where ContactInclude in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set ContactInclude = '0' where ContactInclude in ('No','N','FALSE');

			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the ContactInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE ContactInclude = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN ContactInclude bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the ContactInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'LocationInclude') BEGIN
			UPDATE #mc_EvImport set LocationInclude = '' where LocationInclude is null;
			UPDATE #mc_EvImport set LocationInclude = '1' where LocationInclude in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set LocationInclude = '0' where LocationInclude in ('No','N','FALSE');

			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the LocationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE LocationInclude = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN LocationInclude bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the LocationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'CancellationInclude') BEGIN
			UPDATE #mc_EvImport set CancellationInclude = '' where CancellationInclude is null;
			UPDATE #mc_EvImport set CancellationInclude = '1' where CancellationInclude in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set CancellationInclude = '0' where CancellationInclude in ('No','N','FALSE');

			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the CancellationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE CancellationInclude = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN CancellationInclude bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the CancellationInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'TravelInclude') BEGIN
			UPDATE #mc_EvImport set TravelInclude = '' where TravelInclude is null;
			UPDATE #mc_EvImport set TravelInclude = '1' where TravelInclude in ('Yes','Y','TRUE');
			UPDATE #mc_EvImport set TravelInclude = '0' where TravelInclude in ('No','N','FALSE');

			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an blank value in the TravelInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
			FROM #mc_EvImport
			WHERE TravelInclude = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			BEGIN TRY
				ALTER TABLE #mc_EvImport ALTER COLUMN TravelInclude bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblEvErrors (msg)
				VALUES ('There are invalid values in the TravelInclude column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'ParentEventCode') BEGIN
			-- parenteventcode must be at or under 15 chars
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid ParentEventCode. ParentEventCode must be 15 characters or less.'
			FROM #mc_EvImport
			WHERE len(isnull(ParentEventCode,'')) > 15 
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- ParentEventCode cannot be itself
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid ParentEventCode. An event cannot be a child event of itself.'
			FROM #mc_EvImport
			WHERE len(isnull(ParentEventCode,'')) > 0
			and ParentEventCode = EventCode
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- match on ParentEventCode for events created before
			update tmp 
			set tmp.MCParentEventID = e.eventID
			from #mc_EvImport as tmp 
			inner join dbo.ev_events as e on e.reportCode = tmp.ParentEventCode
				and e.status = 'A'
				and e.siteID = @siteID
			inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
				and ce.calendarID = tmp.MCCalendarID
				and ce.calendarID = ce.sourceCalendarID
			where len(isnull(tmp.ParentEventCode,'')) > 0;

			-- match on ParentEventCode for events in this file
			update tmp 
			set tmp.MCParentRowID = tmp2.rowID
			from #mc_EvImport as tmp 
			inner join #mc_EvImport as tmp2 on tmp2.eventCode = tmp.ParentEventCode and tmp.MCCalendarID = tmp2.MCCalendarID
			where len(isnull(tmp.ParentEventCode,'')) > 0;

			-- ParentEventCode cannot be bad
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid ParentEventCode. No matching EventCode in the file or Control Panel.'
			FROM #mc_EvImport
			WHERE len(isnull(ParentEventCode,'')) > 0
			and MCParentEventID is null
			and MCParentRowID is null
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- events with a parent event code must appear in the file after the parent event
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is a sub-event but appears after the master event in the file. Ensure all master events are listed before sub-events.'
			FROM #mc_EvImport
			WHERE MCParentRowID is not null
			and rowID < MCParentRowID
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- sub events cannot be parent events
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(tmp.rowID as varchar(10)) + ' is a sub-event but also appears as a parent event. Sub-events cannot contain sub-events.'
			FROM #mc_EvImport as tmp
			inner join #mc_EvImport as tmp2 on tmp2.ParentEventCode = tmp.EventCode
			WHERE (tmp.MCParentRowID is not null OR tmp.MCParentEventID is not null)
			ORDER BY tmp.rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END

		IF @recurringEvents = 1 AND NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'RecurringSeriesCode') BEGIN
			-- RecurringSeriesCode must be at or under 15 chars
			INSERT INTO #tblEvErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid RecurringSeriesCode. RecurringSeriesCode must be 15 characters or less.'
			FROM #mc_EvImport
			WHERE len(isnull(RecurringSeriesCode,'')) > 15 
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- calc recurrence order of events; primary event (RecurringSeriesCode matching event) is already defined
			WITH evRecurrenceOrder AS (
				SELECT rowID, ROW_NUMBER() OVER (PARTITION BY RecurringSeriesCode ORDER BY EventStart) AS MCRecurrenceOrder
				FROM #mc_EvImport
				WHERE len(RecurringSeriesCode) > 0
				AND RecurringSeriesCode <> eventCode
			)
			UPDATE tmp
			SET tmp.MCRecurrenceOrder = e.MCRecurrenceOrder
			FROM #mc_EvImport AS tmp
			INNER JOIN evRecurrenceOrder AS e ON e.rowID = tmp.rowID;
		END

	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to validate data in optional columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- **************
	-- credit columns
	-- **************
	BEGIN TRY

		select @ASID = min(ASID) from #tblPossibleCredits;
		while @ASID is not null begin
			select @crdAuthorityCode=null, @creditColName = null;

			select @crdAuthorityCode = authorityCode from #tblPossibleCredits where ASID = @ASID;

			IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = @crdAuthorityCode + '_approval') BEGIN
				select @dynSQL = 'UPDATE #mc_EvImport set [' + @crdAuthorityCode + '_approval] = '''' where [' + @crdAuthorityCode + '_approval] is null;';
				EXEC(@dynSQL);

				select @dynSQL = '
					SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' has an invalid value for ' + @crdAuthorityCode + '_approval. Approvals must be 50 characters or less.''
					FROM #mc_EvImport  
					WHERE len(isnull([' + @crdAuthorityCode + '_approval],'''')) > 50 
					ORDER BY rowID';
				INSERT INTO #tblEvErrors (msg)
				EXEC(@dynSQL);
			END

			IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = @crdAuthorityCode + '_status') BEGIN
				select @dynSQL = '
					SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' has an invalid value for ' + @crdAuthorityCode + '_status. Status must be Approved, Denied, Not Submitted, or Pending.''
					FROM #mc_EvImport  
					WHERE [' + @crdAuthorityCode + '_status] not in ('''',''Approved'',''Denied'',''Not Submitted'',''Pending'') 
					ORDER BY rowID';
				INSERT INTO #tblEvErrors (msg)
				EXEC(@dynSQL);
			END

			select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID;
			while @creditColName is not null begin
				IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = @creditColName) BEGIN
					set @dynSQL = '
						BEGIN TRY
							UPDATE #mc_EvImport set [' + @creditColName + '] = null where [' + @creditColName + '] is not null and [' + @creditColName + '] = '''';
							ALTER TABLE #mc_EvImport ALTER COLUMN [' + @creditColName + '] decimal(6,2) NULL;
						END TRY
						BEGIN CATCH
							INSERT INTO #tblEvErrors (msg)
							VALUES (''There are invalid values in the ' + @creditColName + ' column.'')
						END CATCH';
					EXEC(@dynSQL);
				END

				select @creditColName = min(column_name) from #tblPossibleCreditCols where ASID = @ASID and column_name > @creditColName;
			end

			select @ASID = min(ASID) from #tblPossibleCredits where ASID > @ASID;
		end

	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to validate data in credit columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ensure credit columns are included in the queue tables
	BEGIN TRY
		insert into platformQueue.dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID) 
		select @queueTypeID, tmp.column_name, tmp.dataTypeID
		from (
			select authorityCode + '_approval' as column_name, 1 as dataTypeID
			from #tblPossibleCredits
				union all
			select authorityCode + '_status' as column_name, 1 as dataTypeID
			from #tblPossibleCredits
				union all	
			select column_name, 2
			from #tblPossibleCreditCols
		) as tmp
			except 
		select queueTypeID, columnName, dataTypeID
		from platformQueue.dbo.tblQueueTypeDataColumns
		where queueTypeID = @queueTypeID;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to add credit columns to the queue tables.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ****************************
	-- validate cross-event fields
	-- ****************************
	BEGIN TRY		
		select @minColID = min(fieldID) from #tblEvImportCustomCols;
		while @minColID is not null begin
			select @mincol=null, @isRequired=null, @reqMsg = null, @dataTypeCode=null, @displayTypeCode=null;

			select @mincol=columnName, @isRequired=isRequired, @dataTypeCode=dataTypeCode, @displayTypeCode=displayTypeCode
			from #tblEvImportCustomCols 
			where fieldID = @minColID;

			set @reqMsg = '"' + @mincol + '" data is missing.'

			-- required custom column
			IF @isRequired = 1 BEGIN
				set @dynSQL = '
					SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Column ' + @reqMsg + ' ''
					FROM #mc_EvImport
					WHERE len(ltrim(rtrim(isnull(' + quotename(@mincol) + ','''')))) = 0
					ORDER BY rowID';

				INSERT INTO #tblEvErrors (msg)
				EXEC(@dynSQL)
					IF @@ROWCOUNT > 0 GOTO on_done;
			END

			IF @displayTypeCode in ('SELECT','RADIO','CHECKBOX') BEGIN
				set @reqMsg = 'Column "' + @mincol + '" option "';
				
				-- bit columns support only select box and radio buttons
				IF @dataTypeCode = 'BIT' BEGIN 
					set @good = 1;
					set @dynSQL = '
						set @good = 1
						BEGIN TRY
							UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = 1 where ' + quotename(@mincol) + ' = ''TRUE'' OR ' + quotename(@mincol) + ' = ''YES'';
							UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = 0 where ' + quotename(@mincol) + ' = ''FALSE'' OR ' + quotename(@mincol) + ' = ''NO'';
							ALTER TABLE #mc_EvImport ALTER COLUMN ' + quotename(@mincol) + ' bit null
						END TRY
						BEGIN CATCH
							set @good = 0
						END CATCH';
						exec sp_executesql @dynSQL, N'@good bit output', @good output;
					IF @good = 0 BEGIN
						INSERT INTO #tblEvErrors (msg)
						VALUES ('The column ' + @mincol + ' contains invalid boolean values.');	

						GOTO on_done;
					END
				END

				set @dynSQL = '
					SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ' + @reqMsg + ''' + tbl.listitem  + ''" is invalid.''
					from #mc_EvImport
					cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
					inner join dbo.cf_fields as f on f.fieldID = ' + cast(@minColID as varchar(10)) + ' 
					inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
					left outer join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
						and case when ft.dataTypeCode  = ''DATE'' then cast(cast(tbl.listitem as date) as varchar(15)) else tbl.listitem end
							 = case when ft.dataTypeCode = ''STRING'' then cast(fv.valueString as varchar(max))
									 when ft.dataTypeCode = ''DECIMAL2'' then cast(fv.valueDecimal2 as varchar(15))
									 when ft.dataTypeCode = ''INTEGER'' then cast(fv.valueInteger as varchar(10))
									 when ft.dataTypeCode = ''BIT'' then cast(fv.valueBit as varchar(1))
									 when ft.dataTypeCode = ''DATE'' then cast(fv.valueDate as varchar(15))
								else '''' end
					where fv.valueID is null
					and len(ltrim(rtrim(isnull(' + quotename(@mincol) + ','''')))) > 0
					ORDER BY rowID';

				INSERT INTO #tblEvErrors (msg)
				EXEC(@dynSQL)
					IF @@ROWCOUNT > 0 GOTO on_done;
			END

			IF @dataTypeCode ='INTEGER' and @displayTypeCode = 'TEXTBOX' BEGIN 
				set @good = 1;
				set @dynSQL = '
					set @good = 1
					BEGIN TRY
						UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
						UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
						ALTER TABLE #mc_EvImport ALTER COLUMN ' + quotename(@mincol) + ' int null;
					END TRY
					BEGIN CATCH
						set @good = 0
					END CATCH';
					exec sp_executesql @dynSQL, N'@good bit output', @good output;
				IF @good = 0
					INSERT INTO #tblEvErrors (msg)
					VALUES ('The column "' + @mincol + '" contains invalid whole numbers.');
			END

			IF @dataTypeCode ='DECIMAL2' and @displayTypeCode = 'TEXTBOX' BEGIN 
				set @good = 1;
				set @dynSQL = '
					set @good = 1
					BEGIN TRY
						UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
						UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
						ALTER TABLE #mc_EvImport ALTER COLUMN ' + quotename(@mincol) + ' decimal(14,2) null;
					END TRY
					BEGIN CATCH
						set @good = 0
					END CATCH';
					exec sp_executesql @dynSQL, N'@good bit output', @good output;
				IF @good = 0
					INSERT INTO #tblEvErrors (msg)
					VALUES ('The column "' + @mincol + '" contains invalid decimal values.');
			END

			IF @dataTypeCode ='DATE' and @displayTypeCode = 'DATE' BEGIN 
				set @good = 1;
				set @dynSQL = '
					set @good = 1
					BEGIN TRY
						UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
						UPDATE #mc_EvImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
						ALTER TABLE #mc_EvImport ALTER COLUMN ' + quotename(@mincol) + ' date null;
					END TRY
					BEGIN CATCH
						set @good = 0
					END CATCH';
					exec sp_executesql @dynSQL, N'@good bit output', @good output;
				IF @good = 0
					INSERT INTO #tblEvErrors (msg)
					VALUES ('The column "' + @mincol + '" contains invalid dates.');
			END

			if exists (select 1 from #tblEvErrors)
				GOTO on_done;

			select @minColID = min(fieldID) from #tblEvImportCustomCols where fieldID > @minColID;
		end
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to validate import file for custom columns.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ensure custom columns are included in the queue tables
	BEGIN TRY
		insert into platformQueue.dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID) 
		select @queueTypeID, tmp.columnName, tmp.dataTypeID
		from (
			select tmp.columnName, dt.dataTypeID
			from #tblEvImportCustomCols as tmp
			inner join platformQueue.dbo.tblQueueTypesDataColumnDataTypes as dt on dt.dataTypeCode = tmp.dataTypeCode
		) as tmp
			except 
		select queueTypeID, columnName, dataTypeID
		from platformQueue.dbo.tblQueueTypeDataColumns
		where queueTypeID = @queueTypeID;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to add cross-event fields to the queue tables.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- *************************
	-- prepare table for unpivot
	-- *************************
	IF NOT EXISTS (select top 1 * from #tblEvErrors) BEGIN
		BEGIN TRY
			ALTER TABLE #mc_EvImport ALTER COLUMN EventCode varchar(200) NOT NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN EventSubTitle varchar(200) NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN ParentEventCode varchar(200) NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN RecurringSeriesCode varchar(200) NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN EventTitle varchar(200) NOT NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN RegistrationReplyEmail varchar(200) NOT NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN ContactTitle varchar(200) NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN LocationTitle varchar(200) NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN CancellationTitle varchar(200) NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN TravelTitle varchar(200) NULL;
			ALTER TABLE #mc_EvImport ALTER COLUMN InformationTitle varchar(200) NULL;

			select @ASID = null, @crdAuthorityCode=null;

			select @ASID = min(ASID) from #tblPossibleCredits;
			while @ASID is not null begin
				select @crdAuthorityCode = authorityCode from #tblPossibleCredits where ASID = @ASID;

				select @dynSQL = 'ALTER TABLE #mc_EvImport ALTER COLUMN [' + @crdAuthorityCode + '_approval] varchar(50) NULL;';
				EXEC(@dynSQL);
				select @dynSQL = 'ALTER TABLE #mc_EvImport ALTER COLUMN [' + @crdAuthorityCode + '_status] varchar(50) NULL;';
				EXEC(@dynSQL);

				select @ASID = min(ASID) from #tblPossibleCredits where ASID > @ASID;
			end

			select @minColID = null, @dataTypeCode=null, @mincol=null;

			select @minColID = min(fieldID) from #tblEvImportCustomCols;
			while @minColID is not null begin
				select @dataTypeCode = dataTypeCode, @mincol = columnName
				from #tblEvImportCustomCols 
				where fieldID = @minColID;

				if @dataTypeCode = 'STRING' begin
					select @dynSQL = 'ALTER TABLE #mc_EvImport ALTER COLUMN [' + @mincol + '] varchar(255) NULL;';
					EXEC(@dynSQL);
				end

				select @minColID = min(fieldID) from #tblEvImportCustomCols where fieldID > @minColID;
			end
		END TRY
		BEGIN CATCH
			INSERT INTO #tblEvErrors (msg)
			VALUES ('Unable to prepare data for unpivot.');

			INSERT INTO #tblEvErrors (msg)
			VALUES (left(error_message(),300));

			GOTO on_done;
		END CATCH
	END

	-- ************************
	-- generate result xml file 
	-- ************************
	on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblEvErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);
	
	IF OBJECT_ID('tempdb..#tblEventCreditCols') IS NOT NULL 
		DROP TABLE #tblEventCreditCols;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
